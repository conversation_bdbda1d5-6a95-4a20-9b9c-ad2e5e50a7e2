<?php
/**
 * Taxonomies class
 *
 * Registers and manages custom taxonomies
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class SIB_Taxonomies {
    /**
     * Constructor
     */
    public function __construct() {
        // Register taxonomies
        add_action( 'init', array( $this, 'register_taxonomies' ) );

        // Define rooftop taxonomies
        $rooftop_taxonomies = [
            'rooftop_neighborhood', 'rooftop_atmosphere', 'rooftop_best_for', 'rooftop_view_type',
            'rooftop_music_style', 'rooftop_amenities', 'rooftop_accessibility', 'rooftop_menu_type',
            'rooftop_cuisine_style', 'rooftop_dietary_options', 'rooftop_price_range', 'rooftop_dress_code',
            'rooftop_venue_type', 'rooftop_popular'
        ];

        // Add custom columns to taxonomy admin list for all rooftop taxonomies
        foreach ( $rooftop_taxonomies as $taxonomy ) {
            add_filter( "manage_edit-{$taxonomy}_columns", array( $this, 'add_taxonomy_columns' ) );
            add_filter( "manage_{$taxonomy}_custom_column", array( $this, 'render_taxonomy_columns' ), 10, 3 );

            // Add term meta fields
            add_action( "{$taxonomy}_add_form_fields", array( $this, 'add_term_meta_fields' ) );
            add_action( "{$taxonomy}_edit_form_fields", array( $this, 'edit_term_meta_fields' ), 10, 2 );

            // Save term meta
            add_action( "created_{$taxonomy}", array( $this, 'save_term_meta' ), 10, 2 );
            add_action( "edited_{$taxonomy}", array( $this, 'save_term_meta' ), 10, 2 );
        }
    }

    /**
     * Register custom taxonomies
     */
    public function register_taxonomies() {
        // 1. Rooftop Neighborhood taxonomy (hierarchical)
        $labels = array(
            'name'                       => _x( 'Neighborhoods', 'Taxonomy general name', 'spas-in-barcelona-importer' ),
            'singular_name'              => _x( 'Neighborhood', 'Taxonomy singular name', 'spas-in-barcelona-importer' ),
            'search_items'               => __( 'Search Neighborhoods', 'spas-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Neighborhoods', 'spas-in-barcelona-importer' ),
            'all_items'                  => __( 'All Neighborhoods', 'spas-in-barcelona-importer' ),
            'parent_item'                => __( 'Parent Neighborhood', 'spas-in-barcelona-importer' ),
            'parent_item_colon'          => __( 'Parent Neighborhood:', 'spas-in-barcelona-importer' ),
            'edit_item'                  => __( 'Edit Neighborhood', 'spas-in-barcelona-importer' ),
            'view_item'                  => __( 'View Neighborhood', 'spas-in-barcelona-importer' ),
            'update_item'                => __( 'Update Neighborhood', 'spas-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Neighborhood', 'spas-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Neighborhood Name', 'spas-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate neighborhoods with commas', 'spas-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove neighborhoods', 'spas-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used neighborhoods', 'spas-in-barcelona-importer' ),
            'not_found'                  => __( 'No neighborhoods found.', 'spas-in-barcelona-importer' ),
            'no_terms'                   => __( 'No neighborhoods', 'spas-in-barcelona-importer' ),
            'menu_name'                  => __( 'Neighborhoods', 'spas-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Neighborhoods list navigation', 'spas-in-barcelona-importer' ),
            'items_list'                 => __( 'Neighborhoods list', 'spas-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Neighborhoods', 'spas-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => true,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'rooftop-neighborhoods' ),
        );

        register_taxonomy( 'rooftop_neighborhood', 'rooftop', $args );

        // Services taxonomy (formerly Spa Service)
        $labels = array(
            'name'                       => _x( 'Services', 'Taxonomy general name', 'spas-in-barcelona-importer' ),
            'singular_name'              => _x( 'Service', 'Taxonomy singular name', 'spas-in-barcelona-importer' ),
            'search_items'               => __( 'Search Services', 'spas-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Services', 'spas-in-barcelona-importer' ),
            'all_items'                  => __( 'All Services', 'spas-in-barcelona-importer' ),
            'parent_item'                => __( 'Parent Service', 'spas-in-barcelona-importer' ),
            'parent_item_colon'          => __( 'Parent Service:', 'spas-in-barcelona-importer' ),
            'edit_item'                  => __( 'Edit Service', 'spas-in-barcelona-importer' ),
            'view_item'                  => __( 'View Service', 'spas-in-barcelona-importer' ),
            'update_item'                => __( 'Update Service', 'spas-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Service', 'spas-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Service Name', 'spas-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate services with commas', 'spas-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove services', 'spas-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used services', 'spas-in-barcelona-importer' ),
            'not_found'                  => __( 'No services found.', 'spas-in-barcelona-importer' ),
            'no_terms'                   => __( 'No services', 'spas-in-barcelona-importer' ),
            'menu_name'                  => __( 'Services', 'spas-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Services list navigation', 'spas-in-barcelona-importer' ),
            'items_list'                 => __( 'Services list', 'spas-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Services', 'spas-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false, // Services are typically not hierarchical
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'services' ),
        );

        register_taxonomy( 'services', 'spa', $args );

        // Amenities taxonomy (formerly Spa Feature)
        $labels = array(
            'name'                       => _x( 'Amenities', 'Taxonomy general name', 'spas-in-barcelona-importer' ),
            'singular_name'              => _x( 'Amenity', 'Taxonomy singular name', 'spas-in-barcelona-importer' ),
            'search_items'               => __( 'Search Amenities', 'spas-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Amenities', 'spas-in-barcelona-importer' ),
            'all_items'                  => __( 'All Amenities', 'spas-in-barcelona-importer' ),
            'parent_item'                => __( 'Parent Amenity', 'spas-in-barcelona-importer' ),
            'parent_item_colon'          => __( 'Parent Amenity:', 'spas-in-barcelona-importer' ),
            'edit_item'                  => __( 'Edit Amenity', 'spas-in-barcelona-importer' ),
            'view_item'                  => __( 'View Amenity', 'spas-in-barcelona-importer' ),
            'update_item'                => __( 'Update Amenity', 'spas-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Amenity', 'spas-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Amenity Name', 'spas-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate amenities with commas', 'spas-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove amenities', 'spas-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used amenities', 'spas-in-barcelona-importer' ),
            'not_found'                  => __( 'No amenities found.', 'spas-in-barcelona-importer' ),
            'no_terms'                   => __( 'No amenities', 'spas-in-barcelona-importer' ),
            'menu_name'                  => __( 'Amenities', 'spas-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Amenities list navigation', 'spas-in-barcelona-importer' ),
            'items_list'                 => __( 'Amenities list', 'spas-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Amenities', 'spas-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false, // Amenities are typically not hierarchical
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'amenities' ),
        );

        register_taxonomy( 'amenities', 'spa', $args );

        // Neighborhoods taxonomy (formerly Spa Neighborhood)
        $labels = array(
            'name'                       => _x( 'Neighborhoods', 'Taxonomy general name', 'spas-in-barcelona-importer' ),
            'singular_name'              => _x( 'Neighborhood', 'Taxonomy singular name', 'spas-in-barcelona-importer' ),
            'search_items'               => __( 'Search Neighborhoods', 'spas-in-barcelona-importer' ),
            'popular_items'              => __( 'Popular Neighborhoods', 'spas-in-barcelona-importer' ),
            'all_items'                  => __( 'All Neighborhoods', 'spas-in-barcelona-importer' ),
            'parent_item'                => __( 'Parent Neighborhood', 'spas-in-barcelona-importer' ),
            'parent_item_colon'          => __( 'Parent Neighborhood:', 'spas-in-barcelona-importer' ),
            'edit_item'                  => __( 'Edit Neighborhood', 'spas-in-barcelona-importer' ),
            'view_item'                  => __( 'View Neighborhood', 'spas-in-barcelona-importer' ),
            'update_item'                => __( 'Update Neighborhood', 'spas-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Neighborhood', 'spas-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Neighborhood Name', 'spas-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate neighborhoods with commas', 'spas-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove neighborhoods', 'spas-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used neighborhoods', 'spas-in-barcelona-importer' ),
            'not_found'                  => __( 'No neighborhoods found.', 'spas-in-barcelona-importer' ),
            'no_terms'                   => __( 'No neighborhoods', 'spas-in-barcelona-importer' ),
            'menu_name'                  => __( 'Neighborhoods', 'spas-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Neighborhoods list navigation', 'spas-in-barcelona-importer' ),
            'items_list'                 => __( 'Neighborhoods list', 'spas-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Neighborhoods', 'spas-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => true,
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'query_var'         => true,
            'rewrite'           => array(
                'slug' => 'neighborhoods',
                'with_front' => false,
                'hierarchical' => true
            ),
        );

        register_taxonomy( 'neighborhoods', 'spa', $args );

        // Popular taxonomy (replaces post_tag for spas)
        $labels = array(
            'name'                       => _x( 'Popular', 'Taxonomy general name', 'spas-in-barcelona-importer' ),
            'singular_name'              => _x( 'Popular Item', 'Taxonomy singular name', 'spas-in-barcelona-importer' ),
            'search_items'               => __( 'Search Popular Items', 'spas-in-barcelona-importer' ),
            'popular_items'              => null, // No "popular popular items"
            'all_items'                  => __( 'All Popular Items', 'spas-in-barcelona-importer' ),
            'parent_item'                => null, // Non-hierarchical
            'parent_item_colon'          => null, // Non-hierarchical
            'edit_item'                  => __( 'Edit Popular Item', 'spas-in-barcelona-importer' ),
            'view_item'                  => __( 'View Popular Item', 'spas-in-barcelona-importer' ),
            'update_item'                => __( 'Update Popular Item', 'spas-in-barcelona-importer' ),
            'add_new_item'               => __( 'Add New Popular Item', 'spas-in-barcelona-importer' ),
            'new_item_name'              => __( 'New Popular Item Name', 'spas-in-barcelona-importer' ),
            'separate_items_with_commas' => __( 'Separate popular items with commas', 'spas-in-barcelona-importer' ),
            'add_or_remove_items'        => __( 'Add or remove popular items', 'spas-in-barcelona-importer' ),
            'choose_from_most_used'      => __( 'Choose from the most used popular items', 'spas-in-barcelona-importer' ),
            'not_found'                  => __( 'No popular items found.', 'spas-in-barcelona-importer' ),
            'no_terms'                   => __( 'No popular items', 'spas-in-barcelona-importer' ),
            'menu_name'                  => __( 'Popular', 'spas-in-barcelona-importer' ),
            'items_list_navigation'      => __( 'Popular items list navigation', 'spas-in-barcelona-importer' ),
            'items_list'                 => __( 'Popular items list', 'spas-in-barcelona-importer' ),
            'back_to_items'              => __( '&larr; Back to Popular Items', 'spas-in-barcelona-importer' ),
        );

        $args = array(
            'labels'            => $labels,
            'hierarchical'      => false, // Like tags
            'public'            => true,
            'show_ui'           => true,
            'show_admin_column' => true,
            'show_in_nav_menus' => true,
            'show_tagcloud'     => true,
            'show_in_rest'      => true,
            'rewrite'           => array( 'slug' => 'popular' ),
        );

        register_taxonomy( 'popular', 'spa', $args );

        // Flush rewrite rules to ensure the new taxonomy URLs work
        // Only flush once to avoid performance issues
        // Note: Consider a more robust flushing mechanism if multiple taxonomies are frequently changed.
        if ( ! get_option( 'sib_taxonomies_flushed_v2' ) || isset($_GET['flush_rules']) ) {
            // Log before flushing
            error_log('About to flush rewrite rules for SIB taxonomies');

            // Force update taxonomy slugs before flushing (example for neighborhoods)
            global $wp_taxonomies;
            if (isset($wp_taxonomies['neighborhoods'])) {
                $wp_taxonomies['neighborhoods']->rewrite['slug'] = 'neighborhoods';
                $wp_taxonomies['neighborhoods']->rewrite['with_front'] = false;
                $wp_taxonomies['neighborhoods']->rewrite['hierarchical'] = true;
                error_log('Updated neighborhoods taxonomy rewrite rules');
            }
             if (isset($wp_taxonomies['specialties'])) {
                $wp_taxonomies['specialties']->rewrite['slug'] = 'specialties';
                error_log('Updated specialties taxonomy rewrite rules');
            }
            if (isset($wp_taxonomies['services'])) {
                $wp_taxonomies['services']->rewrite['slug'] = 'services';
                error_log('Updated services taxonomy rewrite rules');
            }
            if (isset($wp_taxonomies['amenities'])) {
                $wp_taxonomies['amenities']->rewrite['slug'] = 'amenities';
                error_log('Updated amenities taxonomy rewrite rules');
            }
            if (isset($wp_taxonomies['popular'])) {
                $wp_taxonomies['popular']->rewrite['slug'] = 'popular';
                error_log('Updated popular taxonomy rewrite rules');
            }


            // Flush rewrite rules
            flush_rewrite_rules();
            update_option( 'sib_taxonomies_flushed_v2', true );

            // Log the action
            if (isset($_GET['flush_rules'])) {
                error_log('Rewrite rules flushed manually from SIB taxonomy registration');
            } else {
                error_log('Rewrite rules flushed automatically from SIB taxonomy registration');
            }

            // Redirect to prevent multiple flushes if this was triggered manually
            if (isset($_GET['flush_rules']) && !is_admin()) {
                // Remove the query parameter and redirect
                $redirect_url = remove_query_arg('flush_rules');
                if (!empty($redirect_url)) {
                    wp_redirect($redirect_url);
                    exit;
                }
            }
        }
    }

    /**
     * Add custom columns to taxonomy admin list
     */
    public function add_taxonomy_columns( $columns ) {
        $new_columns = array();

        foreach ( $columns as $key => $value ) {
            $new_columns[ $key ] = $value;

            if ( $key === 'name' ) {
                $new_columns['icon'] = __( 'Icon', 'spas-in-barcelona-importer' );
                $new_columns['description'] = __( 'Description', 'spas-in-barcelona-importer' );
            }
        }

        return $new_columns;
    }

    /**
     * Render custom column content
     */
    public function render_taxonomy_columns( $content, $column_name, $term_id ) {
        switch ( $column_name ) {
            case 'icon':
                $icon = get_term_meta( $term_id, 'icon', true );
                if ( $icon ) {
                    $content = '<i class="fas fa-' . esc_attr( $icon ) . '"></i> ' . esc_html( $icon );
                } else {
                    $content = '—';
                }
                break;

            case 'description':
                $term = get_term( $term_id );
                $content = ! empty( $term->description ) ? esc_html( wp_trim_words( $term->description, 10 ) ) : '—';
                break;
        }

        return $content;
    }

    /**
     * Add term meta fields to add form
     */
    public function add_term_meta_fields() {
        ?>
        <div class="form-field">
            <label for="term_meta_icon"><?php _e( 'Icon', 'spas-in-barcelona-importer' ); ?></label>
            <input type="text" name="term_meta[icon]" id="term_meta_icon" value="">
            <p class="description"><?php _e( 'Enter a Font Awesome icon name (e.g., "spa" for fa-spa)', 'spas-in-barcelona-importer' ); ?></p>
        </div>

        <div class="form-field">
            <label for="term_meta_seo_title"><?php _e( 'SEO Title', 'spas-in-barcelona-importer' ); ?></label>
            <input type="text" name="term_meta[seo_title]" id="term_meta_seo_title" value="">
            <p class="description"><?php _e( 'Custom title tag for this term archive page', 'spas-in-barcelona-importer' ); ?></p>
        </div>

        <div class="form-field">
            <label for="term_meta_seo_description"><?php _e( 'SEO Description', 'spas-in-barcelona-importer' ); ?></label>
            <textarea name="term_meta[seo_description]" id="term_meta_seo_description" rows="5"></textarea>
            <p class="description"><?php _e( 'Custom meta description for this term archive page', 'spas-in-barcelona-importer' ); ?></p>
        </div>
        <?php
    }

    /**
     * Add term meta fields to edit form
     */
    public function edit_term_meta_fields( $term, $taxonomy ) {
        // Get term meta
        $term_meta = get_term_meta( $term->term_id );
        $icon = isset( $term_meta['icon'][0] ) ? $term_meta['icon'][0] : '';
        $seo_title = isset( $term_meta['seo_title'][0] ) ? $term_meta['seo_title'][0] : '';
        $seo_description = isset( $term_meta['seo_description'][0] ) ? $term_meta['seo_description'][0] : '';
        ?>
        <tr class="form-field">
            <th scope="row" valign="top">
                <label for="term_meta_icon"><?php _e( 'Icon', 'spas-in-barcelona-importer' ); ?></label>
            </th>
            <td>
                <input type="text" name="term_meta[icon]" id="term_meta_icon" value="<?php echo esc_attr( $icon ); ?>">
                <p class="description"><?php _e( 'Enter a Font Awesome icon name (e.g., "spa" for fa-spa)', 'spas-in-barcelona-importer' ); ?></p>
                <?php if ( $icon ) : ?>
                    <p><i class="fas fa-<?php echo esc_attr( $icon ); ?>"></i> <?php echo esc_html( $icon ); ?></p>
                <?php endif; ?>
            </td>
        </tr>

        <tr class="form-field">
            <th scope="row" valign="top">
                <label for="term_meta_seo_title"><?php _e( 'SEO Title', 'spas-in-barcelona-importer' ); ?></label>
            </th>
            <td>
                <input type="text" name="term_meta[seo_title]" id="term_meta_seo_title" value="<?php echo esc_attr( $seo_title ); ?>">
                <p class="description"><?php _e( 'Custom title tag for this term archive page', 'spas-in-barcelona-importer' ); ?></p>
            </td>
        </tr>

        <tr class="form-field">
            <th scope="row" valign="top">
                <label for="term_meta_seo_description"><?php _e( 'SEO Description', 'spas-in-barcelona-importer' ); ?></label>
            </th>
            <td>
                <textarea name="term_meta[seo_description]" id="term_meta_seo_description" rows="5"><?php echo esc_textarea( $seo_description ); ?></textarea>
                <p class="description"><?php _e( 'Custom meta description for this term archive page', 'spas-in-barcelona-importer' ); ?></p>
            </td>
        </tr>
        <?php
    }

    /**
     * Save term meta
     */
    public function save_term_meta( $term_id, $tt_id ) {
        if ( isset( $_POST['term_meta'] ) ) {
            $term_meta = $_POST['term_meta'];

            foreach ( $term_meta as $key => $value ) {
                if ( $key === 'seo_description' ) {
                    $value = sanitize_textarea_field( $value );
                } else {
                    $value = sanitize_text_field( $value );
                }

                update_term_meta( $term_id, $key, $value );
            }
        }
    }
}
